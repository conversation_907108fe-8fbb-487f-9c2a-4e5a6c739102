Iremos criar o fluxo para o pix.

O fluxo deverá ser:
1o request para TransferPixController > chama o metodo TransferPix

O metodo TransferPix terá um  strategy pattern para decide qual será o tipo de transferencia pix.

Temos 4 tipos de transferencia pix

Arquivo App\Enums\BankAccount\Transactions\TransactionTypeEnum

    case KEY = 'key';
    case STATIC_QR_CODE = 'static_qr_code';
    case DYNAMIC_QR_CODE = 'dynamic_qr_code';
    case MANUAL = 'manual';

Esas task é pra criar APENAS o tipo MANUA ou seja - (com agência e conta).

Ver como exemplo a factory database/factories/BankAccountTransactionFactory.php

Caminhos e Arquivos para serem usados:

Controllers
namespace App\Http\Controllers\BankAccount\Transfers\Pix\TransferPixController.php

MODEL
namespace App\Models\BankAccountTransaction.php

ENUM
app/Enums/BankAccount/

Services
App\Services\BankAccount\Transfers\Pix\PixTransferService.php
App\Services\BankAccount\Transfers\Pix\ManualPixTransferService.php

Request
App\Http\Requests\BankAccount\Transfers\Pix\ManualPixTransferRequest.php

Resource
App\Http\Resources\BankAccount\Transfers\Pix\ManualPixTransferResource.php

Os documentos criados com instruções e etc devem ficar na pasta .local/transfers
_________________


Iremos criar as rotas:

[POST] /transfers/pix/manual
Esses abaixo servirão para todas os tipos de transação pix.
[POST] /transfers/pix/[request_control_key]
[POST] /transfers/pix/[request_control_key]/confirm
[GET] /transfers/pix/[request_control_key]/status
[GET] /transfers/pix/[request_control_key]/receipt
[GET] /transfers/pix/[request_control_key]/add-favorite

Explicação de cada endpont e fluxo.
[POST] /transfers/pix/manual
* esse é o endpoint inicial, chamado após o user preencher os dados da conta

payload (precisa criar uma request para validar os campos)
    ispb
    account_branch
    account_number
    account_digit
    account_type
    name
    document_number

* Cria o model, salva a transaction e retorna o request_control_key

Na criação do model será criado o request_control_key

response [pode criar uma resource que recebe a transaction e retorna isso]
    request_control_key (id que a gente gera após criar a transação)
    pix_type (manual)
    name (nome do destinatário) - que foi recebido na request
    document_number (document_number do destinatário)  - que foi recebido na request
    date (data da transação, vai retornar a data atual) - now()
    account: [ //dados bancarios.
      ispb
      bank_name // aqui vai precisar buscar no banco, baseado no ispb
      account_branch
      account_number
      account_digit
      account_type
    ]
    favorite // pode retornar esses valores mesmo por enquanto
      is_favorite: false
      name: "Exemplo, vai ser implementado depois"

------------------
IMPORTANTE: Daqui pra baixo, os endpoints vão servir para todos
os tipos de transação PIX
------------------

[POST] /transfers/pix/[request_control_key]
* Realiza a transação
* A gente precisa validar a senha do usuário
* Nesse momento vamos salvar lat/long

Header
  $headers = getAppCuryHeaders(); (existe um help que pega todos os dados do header).
  $arrHeadersKey = [
          'appcury-base-os',
          'appcury-system-version',
          'appcury-brand',
          'appcury-device',
          'appcury-device-id',
          'appcury-unique-id',
          'appcury-readable-version',
          'appcury-is-airplane-mode',
          'appcury-is-emulator',
          'appcury-is-location-enabled',
      ];
  Criar um campoSalvar esses dados em device_infos.

payload
    amount (valor transferido, float)
    pix_message (string, max:140)
    latitude
    longitude
    password
    tfa_type (ENUM('sms', 'email', 'device'))

response
    [status 200, body vazio]

-------------------

[POST] /transfers/pix/[request_control_key]/confirm
* endpoint que confirma a transação
* pode OU NÃO receber um token no payload
  (porque quando a autenticação é via "device" não tem token)

payload
    Verificar tipo de tfa, se for 'sms' ou 'email' token é obrigatório.
    Se for 'device', token vazio.

response
    [status 200, body vazio]

-------------------

[GET] /transfers/pix/[request_control_key]/status
* endpoint para o App consultar o status da transação
* por enquanto, pode retornar o status de acordo com o que está salvo no banco
  a gente vai implementar um método para consultar a API em outra task.

response
    status
    recipient [
        name
        amount
        date ("Y-m-d H:i:s" do momento que a transação foi concluída)
    ]
    message
      Mensagem do erro - não confundir com a mensagem enviada no pix.
      No caso de sucesso, retornar vazio.


-------------------

[GET] /transfers/pix/[request_control_key]/receipt
* endpoint para retornar o comprovante
* pode deixar vazio por enquanto

-------------------

[GET] /transfers/pix/[request_control_key]/add-favorite
* endpoint para salvar um novo favorito
* pode deixar vazio por enquanto
* Em outra task, nós vamos pegar os dados para salvar baseado no request_control_key informado

payload:
    name (nome que vamos salvar o favorito)

response
    [status 200, body vazio]


Importante 1: Já precisamos prever retornos em caso de erros.

Importante 2: As services você pode estruturar como achar que faz sentido.

Importante 3: A parte de webhook e disparar eventos pro App quando o status da transação mudar vamos fazer em outra task.
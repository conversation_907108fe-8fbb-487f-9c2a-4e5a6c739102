<?php

use App\Enums\BankAccount\StatusEnum;
use App\Http\Controllers\BankAccount\BalanceController;
use App\Http\Controllers\BankAccount\BankAccountController;
use App\Http\Controllers\BankAccount\DocumentsUploadController;
use App\Http\Controllers\BankAccount\FaceReconSelfieController;
use App\Http\Controllers\BankAccount\OcrDocumentsController;
use App\Http\Controllers\BankAccount\StatementController;
use App\Http\Controllers\BankAccount\Transfers\AccountTypeController;
use App\Http\Controllers\BankAccount\Transfers\ListBanksController;
use Illuminate\Support\Facades\Route;

Route::prefix('bank-account')->name('bank-account.')->group(function () {
    Route::get('/', [BankAccountController::class, 'index'])->name('index');

    Route::post('/', [BankAccountController::class, 'createAccount'])
        ->middleware('has-bank-account')
        ->name('createAccount');

    Route::post('/accept-terms', [BankAccountController::class, 'acceptTerms'])
        ->middleware('has-bank-account:' . StatusEnum::INITIAL->value)
        ->name('acceptTerms');

    Route::post('/user/upload', DocumentsUploadController::class)
        ->middleware('has-bank-account')
        ->name('user.upload');

    Route::post('/ocr/documents', OcrDocumentsController::class)
        ->middleware('has-bank-account')
        ->name('ocr.documents');

    Route::post('/face-recon/selfie', FaceReconSelfieController::class)
        ->middleware('has-bank-account')
        ->name('face-recon.selfie');

    // Endpoints internos da conta ativa
    Route::middleware([
        'has-bank-account:' . StatusEnum::ACCOUNT_CONFIRMED->value,
        'has-active-bank-account'
    ])->group(function () {
        Route::get('/balance', BalanceController::class)
            ->name('balance');

        Route::prefix('/statement')->name('statement.')->group(function () {
            Route::get('/transactions', [StatementController::class, 'transactions'])
                ->name('transactions');

            Route::get('/future', [StatementController::class, 'future'])
                ->name('future');
        });

        Route::prefix('/transfers')->name('transfers.')->group(function () {
            Route::get('/list-account-types', AccountTypeController::class)->name('list-account-types');
            Route::get('/list-banks', ListBanksController::class)->name('list-banks');
        });
    });
});

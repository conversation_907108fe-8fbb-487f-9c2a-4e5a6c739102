<?php

declare(strict_types=1);

namespace App\Services\BankAccount;

use App\Models\FinancialInstitution;
use Illuminate\Support\Facades\Cache;

final class FinancialInstitutionService
{
    private const CACHE_KEY = 'financial_institutions_list';
    private const CACHE_TTL = 3600 * 24; // 1 day

    public function listBanks(): array
    {
        return Cache::remember(self::CACHE_KEY, self::CACHE_TTL, function () {
            return FinancialInstitution::where('is_active', true)
                ->where('app_is_active', true)
                ->orderByDesc('app_is_featured')
                ->orderBy('name')
                ->get()
                ->toArray();
        });
    }
}
